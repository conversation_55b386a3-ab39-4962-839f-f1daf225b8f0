# 串口MQTT桥接程序架构设计

## 1. 整体架构概述

本程序采用分层模块化架构，遵循SOLID原则，实现MCU通过串口AT指令与MQTT服务器的桥接通信。

### 1.1 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Application Layer                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Bridge Controller                          │ │
│  │           (协调各模块工作)                                │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Business Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   AT Protocol   │  │  MQTT Client    │  │ Config Mgr   │ │
│  │    Parser       │  │    Module       │  │   Module     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Infrastructure Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Serial Comm     │  │   QMQTT Lib     │  │   Qt Core    │ │
│  │   Module        │  │                 │  │   Services   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. 模块设计

### 2.1 配置管理模块 (Config Manager)
- **职责**: 管理JSON配置文件的读取和解析
- **文件位置**: `src/config/`
- **主要类**: `ConfigManager`
- **配置内容**:
  - 串口配置 (端口、波特率、数据位等)
  - 日志配置
  - 预留MQTT服务器配置扩展

### 2.2 AT协议解析器 (AT Protocol Parser)
- **职责**: AT指令的解析、封装和验证
- **文件位置**: `src/protocol/`
- **主要类**: `AtCommandParser`, `AtCommand`, `AtResponse`
- **支持指令**:
  - 基础指令: AT, AT+QMTOPEN, AT+QMTCONN等
  - 数据指令: AT+QMTPUB, AT+QMTSUB等
  - URC处理: +QMTRECV, +QMTSTAT等

### 2.3 串口通信模块 (Serial Communication)
- **职责**: 串口设备的读写操作
- **文件位置**: `src/serial/`
- **主要类**: `SerialManager`, `SerialDevice`
- **功能**:
  - 串口设备管理
  - AT指令发送/接收
  - 数据帧完整性检查

### 2.4 MQTT客户端模块 (MQTT Client)
- **职责**: MQTT协议的实现和管理
- **文件位置**: `src/mqtt/`
- **主要类**: `MqttManager`, `MqttClient`
- **功能**:
  - MQTT连接管理
  - 主题订阅/发布
  - QoS处理

### 2.5 桥接控制器 (Bridge Controller)
- **职责**: 协调各模块，实现业务逻辑
- **文件位置**: `src/bridge/`
- **主要类**: `BridgeController`
- **功能**:
  - AT指令到MQTT操作的映射
  - 状态机管理
  - 错误处理和重连逻辑

## 3. 文件组织结构

```
SerialMqttBridge/
├── SerialMqttBridge.pro          # 主项目文件
├── src/
│   ├── main.cpp                  # 程序入口
│   ├── config/                   # 配置管理模块
│   │   ├── config.pri
│   │   ├── configmanager.h
│   │   ├── configmanager.cpp
│   │   └── appconfig.h
│   ├── protocol/                 # AT协议解析模块
│   │   ├── protocol.pri
│   │   ├── atcommandparser.h
│   │   ├── atcommandparser.cpp
│   │   ├── atcommand.h
│   │   ├── atcommand.cpp
│   │   ├── atresponse.h
│   │   └── atresponse.cpp
│   ├── serial/                   # 串口通信模块
│   │   ├── serial.pri
│   │   ├── serialmanager.h
│   │   ├── serialmanager.cpp
│   │   ├── serialdevice.h
│   │   └── serialdevice.cpp
│   ├── mqtt/                     # MQTT客户端模块
│   │   ├── mqtt.pri
│   │   ├── mqttmanager.h
│   │   ├── mqttmanager.cpp
│   │   ├── mqttclient.h
│   │   └── mqttclient.cpp
│   ├── bridge/                   # 桥接控制器
│   │   ├── bridge.pri
│   │   ├── bridgecontroller.h
│   │   └── bridgecontroller.cpp
│   └── common/                   # 公共组件
│       ├── common.pri
│       ├── logger.h
│       ├── logger.cpp
│       └── constants.h
├── config/
│   └── config.json               # 配置文件
├── doc/                          # 文档
└── include/                      # 第三方头文件
```

## 4. 设计模式应用

### 4.1 单例模式 (Singleton)
- **应用**: ConfigManager, Logger
- **目的**: 确保全局唯一实例

### 4.2 观察者模式 (Observer)
- **应用**: 模块间事件通知
- **目的**: 解耦模块间依赖

### 4.3 策略模式 (Strategy)
- **应用**: AT指令处理策略
- **目的**: 支持不同类型AT指令的处理

### 4.4 状态模式 (State)
- **应用**: MQTT连接状态管理
- **目的**: 清晰的状态转换逻辑

## 5. 接口设计原则

### 5.1 依赖倒置原则 (DIP)
- 高层模块不依赖低层模块，都依赖抽象
- 定义接口类，具体实现依赖接口

### 5.2 接口隔离原则 (ISP)
- 客户端不应依赖它不需要的接口
- 将大接口拆分为多个小接口

### 5.3 开闭原则 (OCP)
- 对扩展开放，对修改关闭
- 通过继承和多态实现功能扩展

## 6. 核心接口定义

### 6.1 ISerialDevice 接口
```cpp
class ISerialDevice {
public:
    virtual ~ISerialDevice() = default;
    virtual bool open() = 0;
    virtual void close() = 0;
    virtual bool isOpen() const = 0;
    virtual qint64 write(const QByteArray& data) = 0;
    virtual QByteArray readAll() = 0;
};
```

### 6.2 IMqttClient 接口
```cpp
class IMqttClient {
public:
    virtual ~IMqttClient() = default;
    virtual bool connectToHost(const QString& host, quint16 port) = 0;
    virtual void disconnectFromHost() = 0;
    virtual bool subscribe(const QString& topic, quint8 qos) = 0;
    virtual bool publish(const QString& topic, const QByteArray& payload, quint8 qos) = 0;
};
```

### 6.3 IAtCommandParser 接口
```cpp
class IAtCommandParser {
public:
    virtual ~IAtCommandParser() = default;
    virtual AtCommand parseCommand(const QString& command) = 0;
    virtual QString formatResponse(const AtResponse& response) = 0;
    virtual bool isValidCommand(const QString& command) = 0;
};
```

## 7. 数据流设计

### 7.1 上行数据流（MCU -> MQTT服务器）
```
MCU设备 -> 串口AT指令 -> AT协议解析器 -> 桥接控制器 -> MQTT客户端 -> MQTT服务器
```

### 7.2 下行数据流（MQTT服务器 -> MCU）
```
MQTT服务器 -> MQTT客户端 -> 桥接控制器 -> AT协议解析器 -> 串口AT响应 -> MCU设备
```

## 8. 状态机设计

### 8.1 MQTT连接状态机
```
初始状态 -> 网络连接中 -> 网络已连接 -> MQTT连接中 -> MQTT已连接 -> 工作状态
    |           |           |           |           |
    v           v           v           v           v
  错误处理 -> 重连逻辑 -> 断开连接 -> 清理资源 -> 初始状态
```

### 8.2 AT指令处理状态机
```
空闲 -> 发送指令 -> 等待响应 -> 处理响应 -> 空闲
  |       |         |         |
  v       v         v         v
超时处理 -> 重发机制 -> 错误处理 -> 状态恢复
```

## 9. 错误处理策略

### 9.1 串口通信错误
- 串口打开失败：记录错误，尝试重新打开
- 数据发送失败：重试机制，超过次数后报错
- 数据接收超时：清空缓冲区，重置状态

### 9.2 MQTT连接错误
- 网络连接失败：指数退避重连
- MQTT认证失败：检查配置，停止重连
- 消息发布失败：重试队列机制

### 9.3 AT协议解析错误
- 指令格式错误：返回ERROR响应
- 参数无效：参数验证，返回具体错误
- 超时处理：清理状态，通知上层

## 10. 性能优化考虑

### 10.1 内存管理
- 使用对象池减少内存分配
- 合理设置缓冲区大小
- 及时释放不用的资源

### 10.2 并发处理
- 串口读写使用独立线程
- MQTT事件处理异步化
- 避免阻塞主线程

### 10.3 数据处理
- 批量处理提高效率
- 压缩冗余数据传输
- 优化字符串操作

## 11. 扩展性设计

### 11.1 协议扩展
- 支持其他AT指令集
- 可配置的指令映射
- 插件化协议处理

### 11.2 传输扩展
- 支持TCP/UDP传输
- 支持TLS加密
- 支持WebSocket协议

### 11.3 功能扩展
- 数据缓存机制
- 统计监控功能
- 远程配置更新