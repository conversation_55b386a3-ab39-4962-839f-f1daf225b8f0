#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include "configmanager.h"

int main(int argc, char *argv[])
{
    QCoreApplication a(argc, argv);

    qInfo() << "串口MQTT桥接程序启动...";

    // 初始化配置管理器
    ConfigManager* configMgr = ConfigManager::instance();

    // 连接配置加载信号
    QObject::connect(configMgr, &ConfigManager::configLoaded, [](bool success) {
        if (success) {
            qInfo() << "配置文件加载成功";

            // 获取并显示配置信息
            ConfigManager* mgr = ConfigManager::instance();
            SerialConfig serialConfig = mgr->getSerialConfig();
            LogConfig logConfig = mgr->getLogConfig();

            qInfo() << "串口配置:";
            qInfo() << "  端口:" << serialConfig.portName;
            qInfo() << "  波特率:" << serialConfig.baudRate;
            qInfo() << "  读超时:" << serialConfig.readTimeout << "ms";

            qInfo() << "日志配置:";
            qInfo() << "  级别:" << logConfig.logLevel;
            qInfo() << "  文件路径:" << logConfig.logFilePath;
            qInfo() << "  控制台输出:" << logConfig.enableConsoleOutput;
        } else {
            qCritical() << "配置文件加载失败";
        }
    });

    // 加载配置文件
    if (!configMgr->loadConfig()) {
        qCritical() << "无法加载配置文件，程序退出";
        return -1;
    }

    qInfo() << "程序初始化完成，按Ctrl+C退出";

    // 5秒后自动退出（用于测试）
    QTimer::singleShot(5000, &a, [&a]() {
        qInfo() << "测试完成，程序退出";
        a.quit();
    });

    return a.exec();
}
