#include <QCoreApplication>
#include <QDebug>
#include <QTimer>
#include "configmanager.h"
#include "serialmanager.h"

int main(int argc, char *argv[])
{
    QCoreApplication a(argc, argv);

    qInfo() << "串口MQTT桥接程序启动...";

    // 初始化配置管理器
    ConfigManager* configMgr = ConfigManager::instance();

    // 创建串口管理器
    SerialManager* serialMgr = new SerialManager(&a);

    // 连接配置加载信号
    QObject::connect(configMgr, &ConfigManager::configLoaded, [&](bool success) {
        if (success) {
            qInfo() << "配置文件加载成功";

            // 获取并显示配置信息
            ConfigManager* mgr = ConfigManager::instance();
            SerialConfig serialConfig = mgr->getSerialConfig();
            LogConfig logConfig = mgr->getLogConfig();

            qInfo() << "串口配置:";
            qInfo() << "  端口:" << serialConfig.portName;
            qInfo() << "  波特率:" << serialConfig.baudRate;
            qInfo() << "  读超时:" << serialConfig.readTimeout << "ms";

            qInfo() << "日志配置:";
            qInfo() << "  级别:" << logConfig.logLevel;
            qInfo() << "  文件路径:" << logConfig.logFilePath;
            qInfo() << "  控制台输出:" << logConfig.enableConsoleOutput;

            // 显示可用串口
            QStringList availablePorts = SerialManager::getAvailablePorts();
            qInfo() << "可用串口:" << availablePorts;

            // 初始化串口管理器
            if (serialMgr->initialize(serialConfig)) {
                qInfo() << "串口管理器初始化成功";

                // 设置自动重连
                serialMgr->setAutoReconnect(true, 3000);

                // 尝试启动串口
                if (serialMgr->start()) {
                    qInfo() << "串口启动成功";

                    // 发送测试AT指令
                    QTimer::singleShot(1000, [serialMgr]() {
                        if (serialMgr->sendAtCommand("AT")) {
                            qInfo() << "发送AT测试指令成功";
                        } else {
                            qWarning() << "发送AT测试指令失败:" << serialMgr->lastError();
                        }
                    });
                } else {
                    qWarning() << "串口启动失败:" << serialMgr->lastError();
                }
            } else {
                qCritical() << "串口管理器初始化失败:" << serialMgr->lastError();
            }
        } else {
            qCritical() << "配置文件加载失败";
        }
    });

    // 连接串口管理器信号
    QObject::connect(serialMgr, &SerialManager::connectionChanged, [](bool connected) {
        qInfo() << "串口连接状态:" << (connected ? "已连接" : "已断开");
    });

    QObject::connect(serialMgr, &SerialManager::atResponseReceived, [](const AtResponse& response) {
        qInfo() << "收到AT响应:" << response.format();
    });

    QObject::connect(serialMgr, &SerialManager::urcReceived, [](const AtResponse& response) {
        qInfo() << "收到URC:" << response.format();
    });

    QObject::connect(serialMgr, &SerialManager::errorOccurred, [](const QString& error) {
        qWarning() << "串口错误:" << error;
    });

    // 加载配置文件
    if (!configMgr->loadConfig()) {
        qCritical() << "无法加载配置文件，程序退出";
        return -1;
    }

    qInfo() << "程序初始化完成，按Ctrl+C退出";

    // 10秒后自动退出（用于测试）
    QTimer::singleShot(10000, &a, [&a, serialMgr]() {
        qInfo() << "测试完成，停止串口管理器";
        serialMgr->stop();
        qInfo() << "程序退出";
        a.quit();
    });

    return a.exec();
}
